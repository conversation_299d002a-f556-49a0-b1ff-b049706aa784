<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\ChatMessage;
use App\Models\ChatConversation;
use App\Models\Patient;
use App\Models\Provider;
use App\Services\ChatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class ChatController extends Controller
{
    /**
     * The chat service instance.
     *
     * @var ChatService
     */
    protected $chatService;

    /**
     * Create a new controller instance.
     *
     * @param ChatService $chatService
     * @return void
     */
    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Get all conversations for the authenticated patient.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $conversations = $this->chatService->getConversationsForPatient($user->patient->id);

        return response()->json($conversations);
    }

    /**
     * Create a new conversation.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $conversation = $this->chatService->createConversation(
            $user->patient->id,
            $request->input('title')
        );

        if (!$conversation) {
            return response()->json(['error' => 'Failed to create conversation'], 500);
        }

        return response()->json($conversation, 201);
    }

    /**
     * Get a specific conversation.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $conversation = $this->chatService->getConversation($id);

        if (!$conversation || $conversation->patient_email !== $user->email) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        return response()->json($conversation);
    }

    /**
     * Add a message to a conversation.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addMessage(Request $request, $id)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $conversation = $this->chatService->getConversation($id);

        if (!$conversation || $conversation->patient_email !== $user->email) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        $updatedConversation = $this->chatService->addMessage(
            $id,
            $request->input('content'),
            'user',
            $request->input('metadata', [])
        );

        if (!$updatedConversation) {
            return response()->json(['error' => 'Failed to add message'], 500);
        }

        return response()->json($updatedConversation);
    }

    /**
     * Delete a conversation.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $conversation = $this->chatService->getConversation($id);

        if (!$conversation || $conversation->patient_email !== $user->email) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        $result = $this->chatService->deleteConversation($id);

        if (!$result) {
            return response()->json(['error' => 'Failed to delete conversation'], 500);
        }

        return response()->json(['message' => 'Conversation deleted successfully']);
    }

    /**
     * Share a conversation to the public feed.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function share(Request $request, $id)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $conversation = $this->chatService->getConversation($id);

        if (!$conversation || $conversation->patient_email !== $user->email) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Mark the conversation as public
        $result = $this->chatService->shareConversation($id);

        if (!$result) {
            return response()->json(['error' => 'Failed to share conversation'], 500);
        }

        // Generate a summary of the conversation for the social post
        $summary = $conversation->generateSummary();

        // Extract health topics from the conversation
        $healthTopics = $conversation->health_concerns;
        if (empty($healthTopics)) {
            // Default health topics if none are found
            $healthTopics = ['General Health', 'Medical Advice'];
        }

        try {
            // Create a social post from the conversation
            $post = \App\Models\SocialContent::create([
                'source' => 'internal',
                'source_id' => 'chat_' . $conversation->id,
                'content_type' => 'text',
                'media_url' => null,
                'caption' => $summary,
                'health_topics' => $healthTopics,
                'relevance_score' => 1.0,
                'engagement_metrics' => [
                    'likes' => 0,
                    'shares' => 0,
                    'saves' => 0,
                ],
                'filtered_status' => 'approved',
                'user_id' => $user->id,
            ]);

            return response()->json([
                'message' => 'Conversation shared successfully',
                'post_id' => $post->id
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error creating social post: ' . $e->getMessage());

            // Even if creating the social post fails, the conversation is still marked as public
            return response()->json([
                'message' => 'Conversation marked as public, but failed to create social post',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Make a conversation private.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function makePrivate(Request $request, $id)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $conversation = $this->chatService->getConversation($id);

        if (!$conversation || $conversation->patient_email !== $user->email) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        $result = $this->chatService->makeConversationPrivate($id);

        if (!$result) {
            return response()->json(['error' => 'Failed to make conversation private'], 500);
        }

        return response()->json(['message' => 'Conversation is now private']);
    }

    /**
     * Share a legacy conversation to the public feed.
     * This method handles sharing from the old chat system.
     *
     * @param Request $request
     * @param string $conversationId
     * @return \Illuminate\Http\JsonResponse
     */
    public function shareLegacyConversation(Request $request, $conversationId)
    {
        $user = Auth::user();

        if ($user->role !== 'patient' || !$user->patient) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Get the conversation from the legacy system
        try {
            // Use the AIChatController to get the conversation
            $aiChatController = app()->make(\App\Http\Controllers\AIChatController::class);
            $conversationResponse = $aiChatController->getConversation($request, $conversationId);

            // Check if the response is successful
            if ($conversationResponse->getStatusCode() !== 200) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            // Get the conversation data
            $conversationData = json_decode($conversationResponse->getContent(), true);

            // Check if the conversation belongs to the user
            if ($conversationData['patient_id'] != $user->patient->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Create a summary from the conversation
            $messages = $conversationData['messages'] ?? [];
            $summary = '';
            $healthTopics = [];

            if (!empty($messages)) {
                // Extract the first few messages for the summary
                $summaryMessages = array_slice($messages, 0, 3);
                $summary = $conversationData['title'] ?? 'Health Chat';

                // Extract health topics from the conversation
                foreach ($messages as $message) {
                    if (isset($message['metadata']['health_topics'])) {
                        $healthTopics = array_merge($healthTopics, $message['metadata']['health_topics']);
                    }
                }

                // Remove duplicates and limit to 5 topics
                $healthTopics = array_unique($healthTopics);
                $healthTopics = array_slice($healthTopics, 0, 5);
            }

            // Mark the conversation as public in the legacy system
            // This is a placeholder - we would need to implement this in the legacy system

            // Create a social post from the conversation
            $post = \App\Models\SocialContent::create([
                'source' => 'internal',
                'source_id' => 'chat_legacy_' . $conversationId,
                'content_type' => 'text',
                'media_url' => null,
                'caption' => $summary,
                'health_topics' => $healthTopics,
                'relevance_score' => 1.0,
                'engagement_metrics' => [
                    'likes' => 0,
                    'shares' => 0,
                    'saves' => 0,
                ],
                'filtered_status' => 'approved',
                'user_id' => $user->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Conversation shared successfully',
                'post_id' => $post->id
            ]);

        } catch (\Exception $e) {
            \Log::error("Error sharing legacy conversation {$conversationId}: " . $e->getMessage());
            return response()->json(['error' => 'Failed to share conversation: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display a listing of the chats for management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function managementIndex(Request $request)
    {
        // Check if user has permission to view chats
        if (!$request->user()->can('view chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Determine which database to use based on chat type
        if ($request->has('type') && $request->type === 'ai') {
            // AI chats are stored in MongoDB
            return $this->getAIChats($request);
        } else {
            // User-to-user chats are stored in MySQL
            return $this->getUserChats($request);
        }
    }

    /**
     * Get AI chats from MongoDB.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function getAIChats(Request $request)
    {
        $query = ChatConversation::query();

        // Filter by patient if provided
        if ($request->has('patient_id') && $request->patient_id) {
            $patient = Patient::findOrFail($request->patient_id);
            $query->where('patient_id', $patient->id);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date);
        }

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('messages.content', 'like', "%{$search}%");
            });
        }

        // Sort by column
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $chats = $query->paginate($perPage);

        return response()->json($chats);
    }

    /**
     * Get user-to-user chats from MySQL.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function getUserChats(Request $request)
    {
        $query = Chat::with(['patient.user', 'provider.user', 'messages']);

        // Filter by patient if provided
        if ($request->has('patient_id') && $request->patient_id) {
            $query->where('patient_id', $request->patient_id);
        }

        // Filter by provider if provided
        if ($request->has('provider_id') && $request->provider_id) {
            $query->where('provider_id', $request->provider_id);
        }

        // Filter by status if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date);
        }

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('patient.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('provider.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('messages', function($q) use ($search) {
                      $q->where('content', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by flagged status if provided
        if ($request->has('flagged') && $request->flagged) {
            $query->where('is_flagged', true);
        }

        // Sort by column
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $chats = $query->paginate($perPage);

        return response()->json($chats);
    }

    /**
     * Display the specified chat for management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function managementShow(Request $request, $id)
    {
        // Check if user has permission to view chats
        if (!$request->user()->can('view chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Determine which database to use based on chat type
        if ($request->has('type') && $request->type === 'ai') {
            // AI chats are stored in MongoDB
            $chat = ChatConversation::findOrFail($id);
        } else {
            // User-to-user chats are stored in MySQL
            $chat = Chat::with(['patient.user', 'provider.user', 'messages'])->findOrFail($id);
        }

        return response()->json($chat);
    }

    /**
     * Moderate a message in a chat.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @param  int  $messageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function moderateMessage(Request $request, $id, $messageId)
    {
        // Check if user has permission to edit chats
        if (!$request->user()->can('edit chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:hide,restore,edit',
            'content' => 'required_if:action,edit|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the message
        $message = ChatMessage::findOrFail($messageId);

        // Check if message belongs to the specified chat
        if ($message->chat_id != $id) {
            return response()->json(['message' => 'Message does not belong to this chat'], 400);
        }

        // Perform the requested action
        switch ($request->action) {
            case 'hide':
                $message->hidden = true;
                $message->hidden_by = $request->user()->id;
                $message->hidden_at = now();
                break;
            case 'restore':
                $message->hidden = false;
                $message->hidden_by = null;
                $message->hidden_at = null;
                break;
            case 'edit':
                $message->content = $request->content;
                $message->edited_by = $request->user()->id;
                $message->edited_at = now();
                break;
        }

        $message->save();

        return response()->json([
            'message' => 'Message moderated successfully',
            'chat_message' => $message,
        ]);
    }

    /**
     * Archive a chat.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function archiveChat(Request $request, $id)
    {
        // Check if user has permission to edit chats
        if (!$request->user()->can('edit chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $chat = Chat::findOrFail($id);
        $chat->is_archived = true;
        $chat->archived_by = $request->user()->id;
        $chat->archived_at = now();
        $chat->save();

        return response()->json([
            'message' => 'Chat archived successfully',
            'chat' => $chat,
        ]);
    }

    /**
     * Flag a chat for review.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function flagChat(Request $request, $id)
    {
        // Check if user has permission to edit chats
        if (!$request->user()->can('edit chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat = Chat::findOrFail($id);
        $chat->is_flagged = true;
        $chat->flagged_by = $request->user()->id;
        $chat->flagged_at = now();
        $chat->flag_reason = $request->reason;
        $chat->save();

        return response()->json([
            'message' => 'Chat flagged for review',
            'chat' => $chat,
        ]);
    }

    /**
     * Remove flag from a chat.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unflagChat(Request $request, $id)
    {
        // Check if user has permission to edit chats
        if (!$request->user()->can('edit chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $chat = Chat::findOrFail($id);
        $chat->is_flagged = false;
        $chat->flagged_by = null;
        $chat->flagged_at = null;
        $chat->flag_reason = null;
        $chat->save();

        return response()->json([
            'message' => 'Flag removed from chat',
            'chat' => $chat,
        ]);
    }

    /**
     * Get chat statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        // Check if user has permission to view chats
        if (!$request->user()->can('view chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date')
            ? \Carbon\Carbon::parse($request->input('start_date'))
            : \Carbon\Carbon::now()->subDays(30);

        $endDate = $request->input('end_date')
            ? \Carbon\Carbon::parse($request->input('end_date'))
            : \Carbon\Carbon::now();

        // Get total chats
        $totalChats = Chat::whereBetween('created_at', [$startDate, $endDate])->count();

        // Get chats by status
        $chatsByStatus = Chat::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        // Get chats by day
        $chatsByDay = Chat::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, count(*) as count')
            ->groupBy(DB::raw('DATE(created_at)'))
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count,
                ];
            });

        // Get flagged chats count
        $flaggedChatsCount = Chat::whereBetween('created_at', [$startDate, $endDate])
            ->where('is_flagged', true)
            ->count();

        // Get AI chat stats if MongoDB is available
        $aiChatStats = [];
        if (class_exists('App\Models\ChatConversation')) {
            $totalAIChats = ChatConversation::whereBetween('created_at', [$startDate, $endDate])->count();
            $aiChatStats = [
                'total_ai_chats' => $totalAIChats,
            ];
        }

        // Count chats by status for the stats object
        $statusCounts = [];
        foreach ($chatsByStatus as $statusData) {
            $statusCounts[$statusData->status] = $statusData->count;
        }

        return response()->json([
            'total' => $totalChats,
            'patient' => $statusCounts['active'] ?? 0,
            'provider' => $statusCounts['provider'] ?? 0,
            'flagged' => $flaggedChatsCount,
            'chats_by_status' => $chatsByStatus,
            'chats_by_day' => $chatsByDay,
            'ai_chat_stats' => $aiChatStats,
            'date_range' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
        ]);
    }

    /**
     * Get messages for a specific chat.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMessages(Request $request, $id)
    {
        // Check if user has permission to view chats
        if (!$request->user()->can('view chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $chat = Chat::findOrFail($id);
        $messages = ChatMessage::where('chat_id', $id)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'chat' => $chat,
            'messages' => $messages,
        ]);
    }

    /**
     * Add a message to a chat (management).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addManagementMessage(Request $request, $id)
    {
        // Check if user has permission to edit chats
        if (!$request->user()->can('edit chats')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'sender_type' => 'required|in:system,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat = Chat::findOrFail($id);

        $message = new ChatMessage([
            'chat_id' => $chat->id,
            'sender_id' => $request->user()->id,
            'sender_type' => $request->sender_type,
            'content' => $request->content,
            'is_system_message' => $request->sender_type === 'system',
        ]);

        $message->save();

        return response()->json([
            'message' => 'Message added successfully',
            'chat_message' => $message,
        ]);
    }

    /**
     * Display the chat page (web route)
     */
    public function webIndex(Request $request): Response
    {
        return Inertia::render('Chat');
    }
}
