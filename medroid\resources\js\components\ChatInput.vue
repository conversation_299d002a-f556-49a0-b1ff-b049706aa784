<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue';

interface Props {
    modelValue: string;
    placeholder?: string;
    disabled?: boolean;
    isLoading?: boolean;
    showTools?: boolean;
    showVersion?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Type your message...',
    disabled: false,
    isLoading: false,
    showTools: true,
    showVersion: false
});

const emit = defineEmits<{
    'update:modelValue': [value: string];
    'send': [];
    'keydown': [event: KeyboardEvent];
}>();

const chatInputRef = ref<HTMLTextAreaElement | null>(null);

// Format text to sentence case
const formatToSentenceCase = (text: string): string => {
    if (!text) return text;

    // Split by sentences (periods, exclamation marks, question marks)
    return text.replace(/([.!?]\s*)([a-z])/g, (match, punctuation, letter) => {
        return punctuation + letter.toUpperCase();
    }).replace(/^[a-z]/, (match) => {
        return match.toUpperCase();
    });
};

const updateValue = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    const formattedValue = formatToSentenceCase(target.value);
    emit('update:modelValue', formattedValue);
};

const handleKeyDown = (event: KeyboardEvent) => {
    emit('keydown', event);
};

const handleSend = () => {
    emit('send');
};

const focus = () => {
    if (chatInputRef.value) {
        chatInputRef.value.focus();
    }
};

// Expose focus method to parent
defineExpose({
    focus
});
</script>

<template>
    <div class="space-y-4">
        <div class="relative">
            <!-- Input Container -->
            <div class="bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200">
                <!-- Tools Bar -->
                <div v-if="showTools" class="flex items-center justify-between px-4 py-2 border-b border-gray-100">
                    <div class="flex items-center space-x-2">
                        <button class="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-150">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 008 10.172V5L8 4z" />
                            </svg>
                            <span>Tools</span>
                        </button>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        :value="modelValue"
                        @input="updateValue"
                        @keydown="handleKeyDown"
                        :placeholder="placeholder"
                        class="w-full px-4 py-4 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed min-h-[60px] max-h-[200px]"
                        rows="1"
                        :disabled="disabled || isLoading"
                    ></textarea>

                    <!-- Bottom Bar -->
                    <div class="flex items-center justify-between px-4 pb-3">
                        <div class="flex items-center space-x-2">
                            <!-- Attach Button -->
                            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                            </button>

                            <!-- Microphone Button -->
                            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                </svg>
                            </button>
                        </div>

                        <!-- Send Button -->
                        <button
                            @click="handleSend"
                            :disabled="isLoading || !modelValue.trim()"
                            :class="[
                                'p-2 rounded-lg transition-colors duration-150',
                                modelValue.trim() && !isLoading
                                    ? 'bg-medroid-orange text-white hover:bg-medroid-orange/90'
                                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            ]"
                        >
                            <svg v-if="!isLoading" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" />
                            </svg>
                            <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disclaimer -->
        <p class="text-xs text-gray-500 text-center leading-relaxed px-4">
            Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our
            <button class="text-gray-600 hover:text-gray-800 underline underline-offset-2">Terms of Service</button> and
            <button class="text-gray-600 hover:text-gray-800 underline underline-offset-2">Privacy Policy</button>.
        </p>
    </div>
</template>

<style scoped>
/* Auto-resize textarea */
textarea {
    field-sizing: content;
}
</style>
