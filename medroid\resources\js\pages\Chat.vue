<script setup>
import { ref, nextTick, onMounted } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import ChatInput from '@/components/ChatInput.vue'
import MedroidLogo from '@/components/MedroidLogo.vue'
import ReferralModal from '@/components/ReferralModal.vue'
import { Head } from '@inertiajs/vue3'
import axios from 'axios'

// Reactive data
const messages = ref([])
const newMessage = ref('')
const isLoading = ref(false)
const chatContainer = ref(null)
const chatInputRef = ref(null)
const conversationId = ref(null)
const showAppointmentSlots = ref(false)
const appointmentSlots = ref([])
const allAvailableSlots = ref([])
const loadingSlots = ref(false)
const showingAllSlots = ref(false)

const loadingConversation = ref(false)
const selectedDate = ref('')
const selectedProvider = ref('all')
const activeTab = ref('earliest') // 'earliest' or 'all'
const availableDates = ref([])
const availableProviders = ref([])
const showReferralModal = ref(false)

const breadcrumbs = [
    {
        title: 'Chat',
        href: '/chat',
    },
]

// Health concern buttons
const healthConcerns = [
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>`,
    text: 'I have a headache',
    color: 'bg-blue-50 text-blue-700 border-blue-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
    </svg>`,
    text: 'Help me sleep better',
    color: 'bg-green-50 text-green-700 border-green-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
    </svg>`,
    text: 'Weight loss tips',
    color: 'bg-purple-50 text-purple-700 border-purple-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>`,
    text: 'Skin rash concerns',
    color: 'bg-orange-50 text-orange-700 border-orange-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h6m-6 0l-.5 8.5A2 2 0 0013.5 21h-3A2 2 0 018.5 15.5L8 7z" />
    </svg>`,
    text: 'Book appointment',
    color: 'bg-teal-50 text-teal-700 border-teal-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>`,
    text: 'Mental health tips',
    color: 'bg-indigo-50 text-indigo-700 border-indigo-200'
  },
]

// Methods
const formatAIMessage = (content) => {
  if (!content) return content

  let formatted = content

  // Replace **text** with <strong>text</strong>
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

  // Handle sections with ### headers
  formatted = formatted.replace(/### (.*?):/g, '<h3>$1:</h3>')

  // Handle numbered lists with bold headers
  formatted = formatted.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g, '<div class="mb-2"><strong>$1. $2</strong></div>')

  // Handle "Why I think so:" sections
  formatted = formatted.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs, '<div class="ml-4 text-gray-600 mb-2">Why I think so: $1</div>')

  // Handle bullet points with **bold** text
  formatted = formatted.replace(/^- \*\*(.*?)\*\*/gm, '<div class="mb-2"><strong>• $1</strong></div>')

  // Handle regular bullet points
  formatted = formatted.replace(/^- (.*$)/gm, '<div class="mb-1 ml-4">• $1</div>')

  // Handle "WHEN TO SEEK" style headers
  formatted = formatted.replace(/\*\*(.*?):\*\*/g, '<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>')

  // Replace line breaks with proper spacing
  formatted = formatted.replace(/\n\n/g, '<br><br>')
  formatted = formatted.replace(/\n/g, '<br>')

  // Clean up extra breaks
  formatted = formatted.replace(/<br><br><br>/g, '<br><br>')

  return formatted
}

const loadAppointmentSlots = async () => {
  console.log('TEST: loadAppointmentSlots called')
  loadingSlots.value = true

  // Add a test message first
  messages.value.push({
    id: Date.now(),
    type: 'ai',
    content: 'Loading available appointment slots for testing...',
    timestamp: new Date()
  })

  await nextTick()
  scrollToBottom()

  try {
    // Load real providers from the API
    console.log('TEST: Loading providers...')
    const providersResponse = await axios.get('/providers-list')
    const providers = providersResponse.data.data || providersResponse.data.providers || providersResponse.data || []
    console.log('TEST: Providers loaded:', providers.length)

    if (providers.length === 0) {
      messages.value.push({
        id: Date.now(),
        type: 'ai',
        content: 'I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.',
        timestamp: new Date()
      })
      return
    }

    // Get available slots for the next 7 days
    const today = new Date()
    const slots = []

    // Load existing appointments once to prevent conflicts
    let existingAppointments = []
    try {
      const appointmentsResponse = await axios.get('/appointments-list')
      existingAppointments = appointmentsResponse.data.appointments || []
      console.log('TEST: Existing appointments:', existingAppointments.length)
    } catch (error) {
      console.log('TEST: Could not load existing appointments, proceeding without conflict check')
    }

    // Prepare available dates (next 14 days)
    const dates = []
    for (let dayOffset = 0; dayOffset <= 14; dayOffset++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() + dayOffset)
      const dateStr = checkDate.toISOString().split('T')[0]
      dates.push({
        value: dateStr,
        label: checkDate.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        }),
        fullDate: checkDate
      })

    }
    availableDates.value = dates

    // Set default selected date to empty (show all dates initially)
    if (!selectedDate.value) {
      selectedDate.value = ''
    }

    // Prepare available providers
    availableProviders.value = [
      { value: 'all', label: 'All Providers' },
      ...providers.map(provider => ({
        value: provider.id.toString(),
        label: `Dr. ${provider.user?.name || 'Provider'}`
      }))
    ]

    // Check multiple days to get more slots from ALL providers
    for (let dayOffset = 0; dayOffset <= 14; dayOffset++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() + dayOffset)
      const dateStr = checkDate.toISOString().split('T')[0]
      console.log(`TEST: Checking date ${dateStr}`)

      for (const provider of providers) {
        console.log(`TEST: Checking provider ${provider.id} - ${provider.user?.name}`)
        try {
          const slotsResponse = await axios.get(`/get-providers/${provider.id}/available-slots?date=${dateStr}`)
          const availableSlots = slotsResponse.data.available_slots || []
          console.log(`TEST: Provider ${provider.id} has ${availableSlots.length} slots on ${dateStr}`)

          if (availableSlots.length > 0) {
            // Filter out slots that conflict with existing appointments
            const filteredSlots = availableSlots.filter(slot => {
              const slotDateTime = `${dateStr} ${slot.start_time}`
              const hasConflict = existingAppointments.some(appointment => {
                if (appointment.status === 'cancelled') return false
                const appointmentDateTime = `${appointment.date || appointment.scheduled_at?.split('T')[0]} ${appointment.time || appointment.start_time}`
                return appointmentDateTime === slotDateTime
              })
              return !hasConflict
            })

            console.log(`TEST: Provider ${provider.id} has ${filteredSlots.length} available slots after filtering`)

            if (filteredSlots.length === 0) {
              console.log(`TEST: All slots for provider ${provider.id} on ${dateStr} are already booked`)
              continue
            }

            // Get provider's services to include service_id
            let service = null
            try {
              const servicesResponse = await axios.get(`/get-providers/${provider.id}/services`)
              const services = servicesResponse.data.services || []
              console.log(`TEST: Services for provider ${provider.id}:`, services)
              if (services.length > 0) {
                service = services[0] // Use first available service
                console.log(`TEST: Selected service:`, service)
              }
            } catch (error) {
              console.error(`Error loading services for provider ${provider.id}:`, error)
            }

            // Take all filtered available slots for this provider on this day
            const providerSlots = filteredSlots.map(slot => ({
              id: `${provider.id}-${dateStr}-${slot.start_time}`,
              provider_id: provider.id,
              service_id: service?.id || 1,
              provider: `Dr. ${provider.user?.name || 'Provider'}`,
              specialty: provider.specialization || 'General Practice',
              date: checkDate.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' }),
              time: slot.start_time,
              end_time: slot.end_time,
              full_date: dateStr,
              datetime: new Date(`${dateStr}T${slot.start_time}`), // Add for sorting
              price: service?.price || 0,
              duration: service?.duration || 15,
              service_name: service?.name || 'Consultation'
            }))


            slots.push(...providerSlots)

            // Don't break here - we want to collect from ALL providers
          }
        } catch (error) {
          console.error(`Error loading slots for provider ${provider.id} on ${dateStr}:`, error)
        }
      }

      // Stop if we have enough slots (but only after checking all providers for this day)
      if (slots.length >= 50) break
    }

    console.log('TEST: Total slots found:', slots.length)

    if (slots.length === 0) {
      messages.value.push({
        id: Date.now(),
        type: 'ai',
        content: 'I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.',
        timestamp: new Date()
      })
      return
    }

    // Sort slots by earliest available time across all providers
    slots.sort((a, b) => {
      return a.datetime - b.datetime
    })

    // Group slots by provider to ensure diversity
    const slotsByProvider = {}
    slots.forEach(slot => {
      if (!slotsByProvider[slot.provider_id]) {
        slotsByProvider[slot.provider_id] = []
      }
      slotsByProvider[slot.provider_id].push(slot)
    })

    // Take slots from each provider alternately to ensure diversity
    const diverseSlots = []
    const maxSlotsPerProvider = Math.ceil(8 / Object.keys(slotsByProvider).length)

    Object.keys(slotsByProvider).forEach(providerId => {
      const providerSlots = slotsByProvider[providerId].slice(0, maxSlotsPerProvider)
      diverseSlots.push(...providerSlots)
    })

    // Sort the diverse slots by datetime again
    diverseSlots.sort((a, b) => a.datetime - b.datetime)


    allAvailableSlots.value = diverseSlots // Store all slots for filtering
    showingAllSlots.value = false // Reset the view more state
    showAppointmentSlots.value = true

    // Apply initial filtering
    filterSlots()
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Error loading appointment slots:', error)
    messages.value.push({
      id: Date.now(),
      type: 'ai',
      content: 'I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.',
      timestamp: new Date()
    })
  } finally {
    loadingSlots.value = false
    await nextTick()
    scrollToBottom()
  }
}

const bookAppointment = async (slot) => {
  try {
    // Create appointment booking request with payment
    const appointmentData = {
      provider_id: slot.provider_id,
      service_id: slot.service_id || 1, // Default service if not provided
      date: slot.full_date,
      time_slot: {
        start_time: slot.time,
        end_time: slot.end_time
      },
      reason: 'Appointment booked through AI chat',
      notes: `Booked via Medroid AI chat on ${new Date().toLocaleDateString()}`,
      currency: 'GBP'
    }

    // Use the web-based appointment creation endpoint that handles payment
    const response = await axios.post('/save-appointment-with-payment', appointmentData)

    if (response.data.appointment) {
      const appointment = response.data.appointment
      const payment = response.data.payment

      if (payment && payment.client_secret) {
        // Payment is required - show payment message and redirect
        const paymentMessage = {
          id: Date.now(),
          type: 'ai',
          content: `🏥 **Appointment Created - Payment Required**

**Provider:** ${slot.provider}
**Date:** ${slot.date}
**Time:** ${slot.time}
**Appointment ID:** #${appointment.id}
**Amount:** £${payment.amount}

Your appointment has been created and is pending payment. You'll be redirected to complete the payment process in a moment.

Please complete the payment to confirm your appointment.`,
          timestamp: new Date(),
          formatted: formatAIMessage(`🏥 **Appointment Created - Payment Required**

**Provider:** ${slot.provider}
**Date:** ${slot.date}
**Time:** ${slot.time}
**Appointment ID:** #${appointment.id}
**Amount:** £${payment.amount}

Your appointment has been created and is pending payment. You'll be redirected to complete the payment process in a moment.

Please complete the payment to confirm your appointment.`)
        }

        messages.value.push(paymentMessage)

        // Save this message to the conversation history
        if (conversationId.value) {
          try {
            await axios.post('/api/chat/message', {
              conversation_id: conversationId.value,
              message: paymentMessage.content,
              role: 'assistant'
            })
          } catch (error) {
            console.error('Error saving payment message to chat history:', error)
          }
        }

        // Redirect to payment page after a short delay
        setTimeout(() => {
          window.location.href = `/appointments/${appointment.id}/payment`
        }, 3000)

      } else {
        // No payment required - show confirmation
        const confirmationMessage = {
          id: Date.now(),
          type: 'ai',
          content: `✅ **Appointment Confirmed!**

**Provider:** ${slot.provider}
**Date:** ${slot.date}
**Time:** ${slot.time}
**Appointment ID:** #${appointment.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`,
          timestamp: new Date(),
          formatted: formatAIMessage(`✅ **Appointment Confirmed!**

**Provider:** ${slot.provider}
**Date:** ${slot.date}
**Time:** ${slot.time}
**Appointment ID:** #${appointment.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`)
        }

        messages.value.push(confirmationMessage)

        // Save this message to the conversation history
        if (conversationId.value) {
          try {
            await axios.post('/api/chat/message', {
              conversation_id: conversationId.value,
              message: confirmationMessage.content,
              role: 'assistant'
            })
          } catch (error) {
            console.error('Error saving confirmation message to chat history:', error)
          }
        }
      }
    } else {
      throw new Error('No appointment data returned')
    }

    showAppointmentSlots.value = false
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Error booking appointment:', error)

    const errorMessage = {
      id: Date.now(),
      type: 'ai',
      content: `I apologize, but there was an error booking your appointment with ${slot.provider}. This could be due to:

• The time slot may no longer be available
• A technical issue with our booking system

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,
      timestamp: new Date(),
      formatted: formatAIMessage(`I apologize, but there was an error booking your appointment with ${slot.provider}. This could be due to:

• The time slot may no longer be available
• A technical issue with our booking system

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)
    }

    messages.value.push(errorMessage)

    showAppointmentSlots.value = false
    await nextTick()
    scrollToBottom()
  }
}

const sendMessage = async (messageText = null) => {
  const messageToSend = messageText || newMessage.value.trim()
  if (!messageToSend) return

  messages.value.push({
    id: Date.now(),
    type: 'user',
    content: messageToSend,
    timestamp: new Date()
  })

  if (!messageText) {
    newMessage.value = ''
  }

  // Scroll immediately after user message
  await nextTick()
  scrollToBottom()

  isLoading.value = true

  try {
    if (!conversationId.value) {
      // First, start a new conversation using Inertia
      const startResponse = await fetch('/api/chat/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        credentials: 'include',
        body: JSON.stringify({})
      })

      const startData = await startResponse.json()

      if (!startResponse.ok) {
        console.error('Start conversation failed:', startData)
        throw new Error(startData.message || 'Failed to start conversation')
      }

      if (!startData.conversation_id) {
        throw new Error('No conversation ID returned')
      }

      // Ensure conversation ID is a string
      conversationId.value = String(startData.conversation_id)
    }

    // Now send the message
    const payload = {
      conversation_id: String(conversationId.value), // Ensure it's a string
      message: messageToSend,
      include_patient_context: true,
      generate_title: true,
      request_full_response: false
    };

    console.log('Sending payload:', payload); // Debug log

    const response = await fetch('/api/chat/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      credentials: 'include',
      body: JSON.stringify(payload)
    })

    const data = await response.json()

    if (response.ok && data.message) {
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: data.message,
        timestamp: new Date(),
        formatted: formatAIMessage(data.message)
      }

      messages.value.push(aiMessage)

      // Check if AI response contains appointment booking keywords or triggers
      const messageText = data.message.toLowerCase()
      const appointmentKeywords = [
        'appointment', 'book', 'schedule', 'available slots',
        'see a doctor', 'consultation', 'visit', 'meet with',
        'appointment options provided', 'would you like to schedule'
      ]

      const containsAppointmentKeyword = appointmentKeywords.some(keyword =>
        messageText.includes(keyword)
      )

      if (data.appointment_options || data.show_appointments || containsAppointmentKeyword) {
        setTimeout(() => {
          loadAppointmentSlots()
        }, 1000)
      }
    } else {
      console.error('Failed to get AI response:', data)
      messages.value.push({
        id: Date.now() + 1,
        type: 'ai',
        content: `Sorry, I encountered an error: ${data.message || 'Please try again.'}`,
        timestamp: new Date()
      })
    }
  } catch (error) {
    console.error('An error occurred while sending your message:', error)
    messages.value.push({
      id: Date.now() + 1,
      type: 'ai',
      content: `Sorry, I encountered an error: ${error.message || 'Please try again.'}`,
      timestamp: new Date()
    })
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()

    // Focus the input after sending message
    if (chatInputRef.value && chatInputRef.value.focus) {
      setTimeout(() => {
        chatInputRef.value.focus()
      }, 100)
    }
  }
}

const handleConcernClick = (concern) => {
  sendMessage(concern.text)
}

const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const startNewChat = () => {
  messages.value = []
  conversationId.value = null
  showAppointmentSlots.value = false
  newMessage.value = ''

  // Focus the input
  if (chatInputRef.value && chatInputRef.value.focus) {
    setTimeout(() => {
      chatInputRef.value.focus()
    }, 100)
  }
}

const openReferralModal = () => {
  showReferralModal.value = true
}

const closeReferralModal = () => {
  showReferralModal.value = false
}

const filterSlots = () => {
  let filteredSlots = [...allAvailableSlots.value]

  // Filter by date if specific date is selected
  if (selectedDate.value && selectedDate.value !== '' && selectedDate.value !== 'all') {
    filteredSlots = filteredSlots.filter(slot => slot.full_date === selectedDate.value)
  }

  // Filter by provider if specific provider is selected
  if (selectedProvider.value && selectedProvider.value !== 'all') {
    filteredSlots = filteredSlots.filter(slot => slot.provider_id.toString() === selectedProvider.value)
  }

  // Show ALL filtered slots - no artificial limits
  appointmentSlots.value = filteredSlots
  showingAllSlots.value = true
}

const onDateChange = () => {
  filterSlots()
}

const onProviderChange = () => {
  filterSlots()
}

const onTabChange = (tab) => {
  activeTab.value = tab
  filterSlots()
}

// Load existing conversation from URL parameter
const loadExistingConversation = async (conversationIdParam) => {
  if (!conversationIdParam) return

  loadingConversation.value = true
  try {
    const response = await axios.get(`/api/chat/conversation/${conversationIdParam}`)
    const data = response.data

    // The API returns the conversation directly, not wrapped in a conversation property
    if (data && data.id) {
      conversationId.value = conversationIdParam

      // Load messages if they exist
      if (data.messages && Array.isArray(data.messages)) {
        messages.value = data.messages.map(msg => ({
          id: msg._id || msg.id || Date.now() + Math.random(),
          type: msg.role === 'user' ? 'user' : 'ai',
          content: msg.content || msg.message,
          timestamp: new Date(msg.timestamp || msg.created_at || Date.now()),
          formatted: msg.role !== 'user' ? formatAIMessage(msg.content || msg.message) : undefined
        }))

        // Scroll to bottom after loading messages
        await nextTick()
        scrollToBottom()
      }
    }
  } catch (error) {
    console.error('Error loading conversation:', error)
    // If conversation doesn't exist or error, start fresh
    conversationId.value = null
    messages.value = []
  } finally {
    loadingConversation.value = false
  }
}

// Check URL parameters on mount
onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search)
  const conversationParam = urlParams.get('conversation')

  if (conversationParam) {
    loadExistingConversation(conversationParam)
  }
})
</script>

<template>
    <Head title="Chat - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="h-full flex flex-col bg-gray-50">
            <!-- Chat Header - Always visible -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="max-w-4xl mx-auto flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-lg font-semibold text-gray-900">Medroid AI</h1>
                            <p class="text-sm text-gray-500">Your AI Doctor</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <!-- New Chat Button -->
                        <button
                            @click="startNewChat"
                            class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span>New Chat</span>
                        </button>

                        <!-- TEST: Show Appointment Slots Button (Temporary) -->
                        <button
                            @click="loadAppointmentSlots"
                            class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 shadow-md hover:shadow-lg"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>TEST Slots</span>
                        </button>

                        <!-- Refer & Earn Button -->
                        <button
                            @click="openReferralModal"
                            class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors duration-200"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>Refer & Earn</span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- Loading Conversation -->
            <div v-if="loadingConversation" class="flex-1 flex flex-col items-center justify-center px-6 py-8">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading conversation...</p>
                </div>
            </div>

            <!-- Main Content Area -->
            <div v-else-if="messages.length === 0" class="flex-1 flex flex-col items-center justify-center px-6 py-8">
                <!-- Welcome Section -->
                <div class="text-center mb-12">
                    <!-- Medroid Logo -->
                    <div class="mx-auto mb-6">
                        <MedroidLogo :size="80" :show-shadow="true" />
                    </div>

                    <h1 class="text-4xl font-bold text-gray-900 mb-4">Welcome to Medroid</h1>
                    <p class="text-lg text-gray-600 max-w-md mx-auto">
                        Your personal AI Doctor. Ask me anything about your health, symptoms, medications, or wellness advice.
                    </p>
                </div>

                <!-- Health Concern Buttons -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12 max-w-2xl">
                    <button
                        v-for="concern in healthConcerns"
                        :key="concern.text"
                        @click="handleConcernClick(concern)"
                        :class="concern.color"
                        class="flex flex-col items-center p-4 rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105"
                    >
                        <div class="mb-2" v-html="concern.icon"></div>
                        <span class="text-sm font-medium text-center">{{ concern.text }}</span>
                    </button>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div v-else class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- Messages Container -->
                    <div ref="chatContainer" class="flex-1 overflow-y-auto py-2 space-y-2">
                        <!-- Constrain messages to match input width -->
                        <div class="max-w-4xl mx-auto px-4">
                            <div v-for="message in messages" :key="message.id" class="flex" :class="message.type === 'user' ? 'justify-end' : 'justify-start'">
                                <!-- AI Message -->
                                <div v-if="message.type === 'ai'" class="flex items-start space-x-2 max-w-[85%]">
                                    <div class="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div
                                            class="text-gray-800 leading-snug prose prose-sm max-w-none text-sm"
                                            v-html="message.formatted || message.content"
                                        ></div>
                                        <div class="text-xs text-gray-400 mt-0.5">{{ formatTime(message.timestamp) }}</div>
                                    </div>
                                </div>

                                <!-- User Message -->
                                <div v-else class="flex justify-end max-w-[85%] ml-auto">
                                    <div class="bg-teal-500 text-white px-3 py-2 rounded-2xl rounded-br-md shadow-sm">
                                        <div class="text-sm">{{ message.content }}</div>
                                        <div class="text-xs text-teal-100 mt-0.5 text-right">{{ formatTime(message.timestamp) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Loading indicator -->
                        <div v-if="isLoading" class="max-w-4xl mx-auto px-4">
                            <div class="flex items-start space-x-2">
                                <div class="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="flex space-x-1">
                                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"></div>
                                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Slots -->
                        <div v-if="showAppointmentSlots" class="max-w-4xl mx-auto px-4">
                            <div class="flex items-start space-x-2">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1 shadow-lg">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-lg max-h-[500px] flex flex-col">
                                        <div class="mb-3">
                                            <div class="flex items-center justify-between mb-2">
                                                <h3 class="text-base font-semibold text-gray-900 flex items-center">
                                                    <span class="mr-2">📅</span>
                                                    Available Appointments
                                                </h3>
                                                <div class="text-right">
                                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                        {{ appointmentSlots.length }} slots found
                                                    </span>
                                                    <div v-if="appointmentSlots.length > 0" class="text-xs text-green-600 font-medium mt-1">
                                                        Earliest: {{ appointmentSlots[0]?.date }} at {{ appointmentSlots[0]?.time }}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Tabs -->
                                            <div class="flex space-x-1 mb-3 bg-gray-100 p-1 rounded-lg">
                                                <button
                                                    @click="onTabChange('earliest')"
                                                    :class="[
                                                        'flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
                                                        activeTab === 'earliest'
                                                            ? 'bg-white text-blue-600 shadow-sm'
                                                            : 'text-gray-600 hover:text-gray-900'
                                                    ]"
                                                >
                                                    🚀 Earliest Available
                                                </button>
                                                <button
                                                    @click="onTabChange('all')"
                                                    :class="[
                                                        'flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
                                                        activeTab === 'all'
                                                            ? 'bg-white text-blue-600 shadow-sm'
                                                            : 'text-gray-600 hover:text-gray-900'
                                                    ]"
                                                >
                                                    📋 All Appointments
                                                </button>
                                            </div>

                                            <!-- Filters -->
                                            <div class="flex space-x-2 mb-3">
                                                <div class="flex-1">
                                                    <label class="block text-xs font-medium text-gray-700 mb-1">Date</label>
                                                    <select
                                                        v-model="selectedDate"
                                                        @change="onDateChange"
                                                        class="w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    >
                                                        <option value="">All Dates</option>
                                                        <option v-for="date in availableDates" :key="date.value" :value="date.value">
                                                            {{ date.label }}
                                                        </option>
                                                    </select>
                                                </div>
                                                <div class="flex-1">
                                                    <label class="block text-xs font-medium text-gray-700 mb-1">Provider</label>
                                                    <select
                                                        v-model="selectedProvider"
                                                        @change="onProviderChange"
                                                        class="w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    >
                                                        <option v-for="provider in availableProviders" :key="provider.value" :value="provider.value">
                                                            {{ provider.label }}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="loadingSlots" class="flex items-center justify-center py-8">
                                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                            <span class="ml-3 text-sm text-gray-600">Finding available slots...</span>
                                        </div>

                                        <div v-else-if="appointmentSlots.length === 0" class="text-center py-8">
                                            <div class="text-gray-400 mb-2">
                                                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                            <p class="text-sm text-gray-500 mb-2">No appointments found for your selection</p>
                                            <p class="text-xs text-gray-400">Try selecting a different date or provider</p>
                                        </div>

                                        <div v-else class="flex-1 overflow-y-auto">
                                            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 pb-2">
                                            <button
                                                v-for="slot in appointmentSlots"
                                                :key="slot.id"
                                                @click="bookAppointment(slot)"
                                                class="group relative bg-white border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:shadow-md transition-all duration-200 text-center min-h-[80px] flex flex-col justify-center"
                                            >
                                                <!-- Provider Color Indicator -->
                                                <div class="absolute top-1 right-1 w-2 h-2 rounded-full" :class="slot.provider_id === 3 ? 'bg-green-500' : 'bg-blue-500'"></div>

                                                <!-- Time (Large and prominent) -->
                                                <div class="text-lg font-bold text-gray-900 group-hover:text-blue-700 mb-1">
                                                    {{ slot.time }}
                                                </div>

                                                <!-- Provider Name (Compact) -->
                                                <div class="text-sm text-gray-600 truncate mb-1">
                                                    {{ slot.provider.replace('Dr. ', '') }}
                                                </div>

                                                <!-- Price -->
                                                <div class="text-sm font-semibold text-green-600">
                                                    £{{ slot.price }}
                                                </div>

                                                <!-- Date (only if different from selected date) -->
                                                <div v-if="selectedDate === ''" class="text-xs text-gray-500 mt-1 truncate">
                                                    {{ slot.date.split(',')[0] }}
                                                </div>

                                                <!-- Hover effect -->
                                                <div class="absolute inset-0 bg-blue-50 opacity-0 group-hover:opacity-50 rounded-lg transition-opacity duration-200"></div>
                                            </button>
                                            </div>
                                        </div>

                                        <div class="mt-4 pt-3 border-t border-gray-200">
                                            <div class="flex items-center justify-between mb-2">
                                                <p class="text-xs text-gray-500">
                                                    Click on any slot to book your appointment
                                                </p>
                                                <button
                                                    @click="showAppointmentSlots = false"
                                                    class="text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                                                >
                                                    Cancel
                                                </button>
                                            </div>


                                            <!-- Provider Legend -->
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                                    <div class="flex items-center space-x-1">
                                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                        <span>Dr. Raja Mohan</span>
                                                    </div>
                                                    <div class="flex items-center space-x-1">
                                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                        <span>Dr. Sarah Johnson</span>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    Showing {{ appointmentSlots.length }} available slots
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">{{ formatTime(new Date()) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input (Fixed at bottom) -->
            <div class="border-t border-gray-200 bg-white px-6 py-4">
                <div class="max-w-4xl mx-auto">
                    <ChatInput
                        ref="chatInputRef"
                        v-model="newMessage"
                        placeholder="Type your health question..."
                        :is-loading="isLoading"
                        :show-tools="true"
                        :show-version="true"
                        @send="sendMessage"
                        @keydown="handleKeyDown"
                    />
                </div>
            </div>
        </div>

        <!-- Referral Modal -->
        <ReferralModal
            :is-open="showReferralModal"
            @close="closeReferralModal"
        />
    </AppLayout>
</template>

<style scoped>
/* Hide scrollbar but keep functionality */
.overflow-y-auto {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.overflow-y-auto::-webkit-scrollbar {
    display: none; /* WebKit */
}

/* Smooth animations */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Loading animation for dots */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* Prose styling for AI messages */
.prose {
    color: inherit;
}

.prose strong {
    font-weight: 600;
    color: #1f2937;
}

.prose ul {
    margin: 0.25rem 0;
}

.prose ol {
    margin: 0.25rem 0;
}

.prose li {
    margin: 0.125rem 0;
}

.prose p {
    margin: 0.25rem 0;
}

.prose h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0.5rem 0 0.25rem 0;
    color: #1f2937;
}

.prose h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.375rem 0 0.125rem 0;
    color: #374151;
}
</style>