<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { SelectValue, type SelectValueProps } from 'reka-ui'
import { reactiveOmit } from '@vueuse/core'

const props = defineProps<
  SelectValueProps & { class?: HTMLAttributes['class'] }
>()

const delegatedProps = reactiveOmit(props, 'class')
</script>

<template>
  <SelectValue
    data-slot="select-value"
    v-bind="delegatedProps"
    :class="cn('text-sm placeholder:text-muted-foreground', props.class)"
  >
    <slot />
  </SelectValue>
</template>
